# Latest Spring Boot Version Information

As of May 2024, the latest stable version of Spring Boot is **3.2.3** (released in February 2024).

The upcoming version in development is Spring Boot 3.3.x, which is expected to be released in Q2 2024.

For the most up-to-date information, you can check:
- Official Spring Boot website: https://spring.io/projects/spring-boot
- Spring Boot GitHub repository: https://github.com/spring-projects/spring-boot
- Spring Boot Release Notes: https://github.com/spring-projects/spring-boot/wiki/Spring-Boot-3.2-Release-Notes

## Key Features in Recent Versions

Spring Boot 3.x includes:
- Java 17+ baseline
- Jakarta EE 9+ alignment
- Native compilation support with GraalVM
- Improved observability with Micrometer and Micrometer Tracing
- AOT (Ahead-of-Time) processing

To use the latest version in a Maven project, add:

```xml
<parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>3.2.3</version>
</parent>
```

For Gradle:

```groovy
plugins {
    id 'org.springframework.boot' version '3.2.3'
    id 'io.spring.dependency-management' version '1.1.4'
}
```