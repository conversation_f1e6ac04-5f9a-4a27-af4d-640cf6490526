# Database Configuration
# Uncomment and modify these properties when ready to use

# MySQL Configuration
#spring.datasource.url=*************************************
#spring.datasource.username=username
#spring.datasource.password=password
#spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# PostgreSQL Configuration
#spring.datasource.url=******************************************
#spring.datasource.username=username
#spring.datasource.password=password
#spring.datasource.driver-class-name=org.postgresql.Driver

# H2 Configuration (for testing)
#spring.datasource.url=jdbc:h2:mem:testdb
#spring.datasource.username=sa
#spring.datasource.password=
#spring.datasource.driver-class-name=org.h2.Driver
#spring.h2.console.enabled=true
#spring.h2.console.path=/h2-console

# JPA/Hibernate Properties
#spring.jpa.hibernate.ddl-auto=update
#spring.jpa.show-sql=true
#spring.jpa.properties.hibernate.format_sql=true

# MySQL Dialect
#spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect

# PostgreSQL Dialect
#spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect

# H2 Dialect
#spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect