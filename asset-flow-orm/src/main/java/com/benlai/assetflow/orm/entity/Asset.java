package com.benlai.assetflow.orm.entity;

/**
 * Entity class representing an asset in the system.
 * This is a placeholder class that would be annotated with JPA annotations
 * when the dependencies are uncommented in the pom.xml.
 */
// @Entity
// @Table(name = "assets")
public class Asset {
    
    // @Id
    // @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    private String name;
    private String description;
    private Double value;
    private String category;
    
    // Default constructor
    public Asset() {
    }
    
    // Constructor with fields
    public Asset(String name, String description, Double value, String category) {
        this.name = name;
        this.description = description;
        this.value = value;
        this.category = category;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Double getValue() {
        return value;
    }
    
    public void setValue(Double value) {
        this.value = value;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    @Override
    public String toString() {
        return "Asset{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", value=" + value +
                ", category='" + category + '\'' +
                '}';
    }
}